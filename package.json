{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"android": "expo run:android", "ios": "expo run:ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@gorhom/bottom-sheet": "^5.0.6", "@react-native-community/datetimepicker": "8.3.0", "@react-native-firebase/app": "^22.1.0", "@react-native-firebase/messaging": "^22.1.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@tanstack/react-query": "^5.54.1", "axios": "^1.6.8", "expo": "53.0.12", "expo-blur": "~14.1.5", "expo-brightness": "~13.1.4", "expo-build-properties": "~0.14.6", "expo-camera": "~16.1.8", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.5", "expo-dev-client": "~5.2.1", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-haptics": "~14.1.4", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.5", "expo-media-library": "~17.1.7", "expo-modules-core": "~2.4.0", "expo-notifications": "~0.31.3", "expo-router": "~5.1.0", "expo-splash-screen": "~0.30.9", "expo-system-ui": "~5.0.9", "lodash": "^4.17.21", "moment": "^2.30.1", "nativewind": "latest", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-calendars": "^1.1305.0", "react-native-edge-to-edge": "^1.6.1", "react-native-gesture-handler": "~2.24.0", "react-native-input-select": "^1.3.18", "react-native-keyboard-controller": "^1.17.5", "react-native-mmkv": "^3.3.0", "react-native-modal-datetime-picker": "18.0.0", "react-native-qrcode-styled": "^0.3.2", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.1", "react-native-virtualized-view": "^1.0.0", "react-native-web": "~0.20.0", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.3", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-universe": "^12.0.1", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "eslintConfig": {"extends": "universe/native", "root": true}, "private": true}